#
# AIMX AgenticFlow.ps1 - Interactive Chat Mode
#
# Interactive chat script for AIMX conversation testing with MCP servers
# Supports continuous conversation with user input prompts
# <PERSON><PERSON><PERSON> (rizhang) with help of GitHub Copilot
#

param(
    [string]$InitialQuery = "",
    [int]$TimeoutSeconds = 1200,
    [int]$PollIntervalMs = 125,
    [switch]$Verbose = $false,
    [switch]$ShowRawMessages = $false,
    [switch]$SingleQuery = $false  # For backward compatibility
)

# Import AIMX module
Import-Module .\aimx.psd1 -Force

# Color scheme for elegant output
$Colors = @{
    Header = 'Cyan'
    Success = 'Green'
    Warning = 'Yellow'
    Error = 'Red'
    Info = 'White'
    Timestamp = 'DarkGray'
    Progress = 'Blue'
    Tool = 'Magenta'
    Planning = 'DarkCyan'
    Analysis = 'DarkYellow'
}

# Conversation state tracking
$script:ConversationState = @{
    IsComplete = $false
    HasError = $false
    ErrorMessage = ""
    StartTime = Get-Date
    LastActivity = Get-Date
    MessageCount = 0
    AllMessages = @()
    CurrentPhase = "Initializing"
    ToolExecutions = @()
    PlanningSteps = @()
    IsAIProcessing = $false
    SpinnerPosition = 0
}

# Global context for chat session
$script:GlobalContext = $null
$script:SessionStats = @{
    TotalQueries = 0
    TotalMessages = 0
    SessionStartTime = Get-Date
    Queries = @()
}

# Reset conversation state for new query
function Reset-ConversationState {
    $script:ConversationState = @{
        IsComplete = $false
        HasError = $false
        ErrorMessage = ""
        StartTime = Get-Date
        LastActivity = Get-Date
        MessageCount = 0
        AllMessages = @()
        CurrentPhase = "Initializing"
        ToolExecutions = @()
        PlanningSteps = @()
        IsAIProcessing = $false
        SpinnerPosition = 0
    }
}

# Spinner animation for AI processing
$script:SpinnerChars = @('|', '/', '-', '\')
$script:SpinnerRunning = $false
$script:SpinnerStartTime = $null

# Start the AI processing spinner
function Start-AISpinner {
    param([string]$Message = "AI is thinking")

    if ($script:SpinnerRunning) { return }

    $script:SpinnerRunning = $true
    $script:ConversationState.IsAIProcessing = $true
    $script:ConversationState.SpinnerPosition = 0
    $script:SpinnerStartTime = Get-Date  # Reset spinner timer to 0

    Write-Host ""
    Write-Host "  $($script:SpinnerChars[0]) $Message..." -NoNewline -ForegroundColor $Colors.Progress
}

# Stop the AI processing spinner
function Stop-AISpinner {
    if (-not $script:SpinnerRunning) { return }

    $script:SpinnerRunning = $false
    $script:ConversationState.IsAIProcessing = $false

    # Clear the spinner line cleanly
    Write-Host "`r" -NoNewline
    Write-Host (" " * 60) -NoNewline  # Use consistent padding
    Write-Host "`r" -NoNewline
}

# Temporarily pause the spinner without changing AI processing state
function Pause-AISpinner {
    if (-not $script:SpinnerRunning) { return }

    $script:SpinnerRunning = $false

    # Clear the spinner line
    Write-Host "`r" -NoNewline
    Write-Host (" " * 80) -NoNewline
    Write-Host "`r" -NoNewline
}

# Resume the spinner if AI is still processing
function Resume-AISpinner {
    if ($script:ConversationState.IsAIProcessing -and -not $script:SpinnerRunning) {
        $script:SpinnerRunning = $true
        $script:ConversationState.SpinnerPosition = 0
        Write-Host "  $($script:SpinnerChars[0]) AI is thinking..." -NoNewline -ForegroundColor $Colors.Progress
    }
}

# Update spinner display (called during polling)
function Update-AISpinner {
    if (-not $script:SpinnerRunning) { return }

    $script:ConversationState.SpinnerPosition++
    $spinnerChar = $script:SpinnerChars[$script:ConversationState.SpinnerPosition % $script:SpinnerChars.Length]
    $elapsed = ((Get-Date) - $script:SpinnerStartTime).TotalSeconds

    # Create the full spinner line with consistent length
    $spinnerLine = "  $spinnerChar AI is thinking... ($([math]::Round($elapsed, 1))s)"
    $paddedLine = $spinnerLine.PadRight(60)  # Ensure consistent length to prevent flashing

    # Update the spinner character and time without flashing
    Write-Host "`r$paddedLine" -NoNewline -ForegroundColor $Colors.Progress
}

# Enhanced message categorization with AIMX standardized stage prefixes
function Get-MessageCategory {
    param($message)

    # Extract stage prefix from message content (new standardized approach)
    $content = $message.content
    $stagePrefix = ""

    # Extract stage prefix using regex
    if ($content -match '^\[([^]]+)\]') {
        $stagePrefix = $matches[1]
    }

    # Categorize based on stage prefix
    if ($stagePrefix) {
        switch -Regex ($stagePrefix) {
            "^REQUEST_"           { return @{ Category = "REQUEST"; Color = $Colors.Info; Stage = $stagePrefix } }
            "^PLANNING_"          { return @{ Category = "PLANNING"; Color = $Colors.Info; Stage = $stagePrefix } }
            "^LLM_"               { return @{ Category = "AI"; Color = $Colors.Info; Stage = $stagePrefix } }
            "^EXECUTION_COMPLETED" { return @{ Category = "COMPLETE"; Color = $Colors.Info; Stage = $stagePrefix } }
            "^EXECUTION_"         { return @{ Category = "EXECUTION"; Color = $Colors.Info; Stage = $stagePrefix } }
            default               { return @{ Category = "INFO"; Color = $Colors.Info; Stage = $stagePrefix } }
        }
    }

    # Fallback to legacy pattern matching for backward compatibility
    return Get-LegacyMessageCategory -messageType $message.type -content $message.content
}

# Legacy categorization for backward compatibility (simplified)
function Get-LegacyMessageCategory {
    param($messageType, $content)

    switch ($messageType) {
        "User Input" { return @{ Category = "INPUT"; Color = $Colors.Info; Stage = "" } }
        "Assistant Response" { return @{ Category = "RESPONSE"; Color = $Colors.Info; Stage = "" } }
        "Tool Analysis" { return @{ Category = "ANALYSIS"; Color = $Colors.Info; Stage = "" } }
        "Execution Plan" { return @{ Category = "PLANNING"; Color = $Colors.Info; Stage = "" } }
        "LLM Inference" { return @{ Category = "AI"; Color = $Colors.Info; Stage = "" } }
        "LLM Response" { return @{ Category = "AI"; Color = $Colors.Info; Stage = "" } }
        "Error Message" { return @{ Category = "ERROR"; Color = $Colors.Error; Stage = "" } }
        "Progress Update" { return @{ Category = "PROGRESS"; Color = $Colors.Info; Stage = "" } }
        "Status Update" { return @{ Category = "STATUS"; Color = $Colors.Info; Stage = "" } }
        "Tool Execution" { return @{ Category = "EXECUTION"; Color = $Colors.Info; Stage = "" } }
        "Tool Execution Start" { return @{ Category = "EXECUTION"; Color = $Colors.Info; Stage = "" } }
        "Tool Execution Result" { return @{ Category = "EXECUTION"; Color = $Colors.Info; Stage = "" } }
        default { return @{ Category = "INFO"; Color = $Colors.Info; Stage = "" } }
    }
}

# Beautiful header display
function Show-Header {
    param([string]$Mode = "Interactive Chat")

    $boxWidth = 75
    $title = "AIMX $Mode"
    $padding = " " * [math]::Max(0, $boxWidth - $title.Length - 2)

    Write-Host ""
    Write-Host "  $([char]0x2554)$([string][char]0x2550 * $boxWidth)$([char]0x2557)" -ForegroundColor $Colors.Header
    Write-Host "  $([char]0x2551) $title$padding $([char]0x2551)" -ForegroundColor $Colors.Header
    Write-Host "  $([char]0x255A)$([string][char]0x2550 * $boxWidth)$([char]0x255D)" -ForegroundColor $Colors.Header
    Write-Host ""

    if ($Mode -eq "Interactive Chat") {
        Write-Host "  Mode: " -NoNewline -ForegroundColor $Colors.Info
        Write-Host "Interactive Chat (type 'exit' or 'quit' to end)" -ForegroundColor $Colors.Header
        Write-Host "  Commands: " -NoNewline -ForegroundColor $Colors.Info
        Write-Host "'help' for assistance, 'status' for system info" -ForegroundColor $Colors.Header
    } else {
        Write-Host "  Query: " -NoNewline -ForegroundColor $Colors.Info
        Write-Host $InitialQuery -ForegroundColor $Colors.Header
    }

    Write-Host "  Timeout: " -NoNewline -ForegroundColor $Colors.Info
    Write-Host "$TimeoutSeconds seconds per query" -ForegroundColor $Colors.Header
    Write-Host ""
}

# Get user input with elegant prompt
function Get-UserInput {
    param([bool]$IsFirstInput = $false)

    if ($IsFirstInput -and $InitialQuery) {
        Write-Host "  " -NoNewline
        Write-Host "You" -ForegroundColor $Colors.Header -NoNewline
        Write-Host ": " -NoNewline -ForegroundColor $Colors.Info
        Write-Host $InitialQuery -ForegroundColor $Colors.Info
        return $InitialQuery
    }

    Write-Host ""
    Write-Host "  " -NoNewline
    Write-Host "You" -ForegroundColor $Colors.Header -NoNewline
    Write-Host ": " -NoNewline -ForegroundColor $Colors.Info

    $input = Read-Host

    # Handle special commands
    switch ($input.ToLower().Trim()) {
        "exit" { return $null }
        "quit" { return $null }
        "help" {
            Show-HelpMessage
            return Get-UserInput -IsFirstInput $false
        }
        "status" {
            Show-SystemStatus -context $script:GlobalContext
            return Get-UserInput -IsFirstInput $false
        }
        "" {
            Write-Host "  Please enter a query or 'exit' to quit." -ForegroundColor $Colors.Warning
            return Get-UserInput -IsFirstInput $false
        }
    }

    return $input
}

# Show help message
function Show-HelpMessage {
    Write-Host ""
    Write-Host "  Available Commands:" -ForegroundColor $Colors.Header
    Write-Host "    help   - Show this help message" -ForegroundColor $Colors.Info
    Write-Host "    status - Show system status" -ForegroundColor $Colors.Info
    Write-Host "    exit   - Exit the chat session" -ForegroundColor $Colors.Info
    Write-Host "    quit   - Exit the chat session" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "  You can ask questions about:" -ForegroundColor $Colors.Header
    Write-Host "    - Active Directory operations" -ForegroundColor $Colors.Info
    Write-Host "    - System information" -ForegroundColor $Colors.Info
    Write-Host "    - General queries" -ForegroundColor $Colors.Info
    Write-Host ""
}

# Elegant message display
function Show-Message {
    param($message, $isVerbose = $false)

    if ($isVerbose -and -not $Verbose) { return }

    $timestamp = (Get-Date).ToString("HH:mm:ss.fff")
    $category = Get-MessageCategory -message $message

    # Track conversation phases with enhanced stage information
    Update-ConversationPhase -message $message -stage $category.Stage

    # Format content for display (removes stage prefixes)
    $displayContent = Format-MessageContent -content $message.content -type $message.type

    # Enhanced formatted output with stage information
    Write-Host "  " -NoNewline
    Write-Host $timestamp -ForegroundColor $Colors.Timestamp -NoNewline
    Write-Host " [" -NoNewline -ForegroundColor $Colors.Info
    Write-Host ("{0,-9}" -f $category.Category) -NoNewline -ForegroundColor $category.Color
    Write-Host "] " -NoNewline -ForegroundColor $Colors.Info

    # Show detailed stage information if available
    if ($category.Stage -and $Verbose) {
        Write-Host "($($category.Stage)) " -NoNewline -ForegroundColor $Colors.Timestamp
    }

    Write-Host $displayContent -ForegroundColor $category.Color

    # Show enhanced debug information if requested
    if ($ShowRawMessages) {
        Write-Host "    Raw: $($message.content)" -ForegroundColor $Colors.Timestamp
        if ($category.Stage) {
            Write-Host "    Stage: $($category.Stage)" -ForegroundColor $Colors.Timestamp
        }
    }
}

# Format message content for better readability
function Format-MessageContent {
    param($content, $type)

    # Remove AIMX stage prefixes for cleaner display
    $content = $content -replace '^\[REQUEST_[^\]]*\]\s*', ''
    $content = $content -replace '^\[PLANNING_[^\]]*\]\s*', ''
    $content = $content -replace '^\[LLM_[^\]]*\]\s*', ''
    $content = $content -replace '^\[EXECUTION_[^\]]*\]\s*', ''
    $content = $content -replace '^\[EXECUTION_COMPLETED\]\s*', ''

    # Clean up common formatting issues
    $content = $content -replace '\s+', ' '  # Multiple spaces to single
    $content = $content.Trim()

    # Apply general formatting improvements without command-specific logic
    $content = Format-GeneralContent -content $content -type $type

    # Special formatting for different message types (general patterns only)
    switch ($type) {
        "Progress Update" {
            if ($content -match "Tool '(.+?)' completed successfully \(result: (.+)\)") {
                $toolName = $matches[1]
                $result = $matches[2]

                # Try to parse JSON result for better display
                try {
                    $jsonResult = $result | ConvertFrom-Json
                    if ($jsonResult.content -and $jsonResult.content.message) {
                        return "Tool '$toolName' completed → $($jsonResult.content.message)"
                    }
                    elseif ($jsonResult.message) {
                        return "Tool '$toolName' completed → $($jsonResult.message)"
                    }
                }
                catch {
                    # Fallback to truncated result
                    $shortResult = if ($result.Length -gt 50) { $result.Substring(0, 50) + "..." } else { $result }
                    return "Tool '$toolName' completed → $shortResult"
                }
            }
        }
        "Tool Analysis" {
            if ($content -match "Tool \d+: (.+?) \(on (.+?)\)") {
                return "Found tool: $($matches[1]) on server $($matches[2])"
            }
        }
        "Execution Plan" {
            if ($content -match "Step \d+: (.+)") {
                return "Plan step: $($matches[1])"
            }
        }
        "Assistant Response" {
            # For assistant responses, apply general formatting improvements
            return Format-AssistantResponse -content $content
        }
    }

    return $content
}

# Format content with general improvements (no command-specific logic)
function Format-GeneralContent {
    param($content, $type)

    # Simple general formatting - just clean up spacing and line breaks
    $content = $content -replace '(\r?\n){3,}', "`n`n"  # Reduce excessive line breaks
    $content = $content -replace '^\s+', ''  # Remove leading whitespace
    $content = $content -replace '\s+$', ''  # Remove trailing whitespace
    $content = $content.Trim()

    return $content
}

# Format assistant responses with better structure and readability
function Format-AssistantResponse {
    param($content)

    # Check if content contains JSON response format
    if ($content -match '```json\s*(\{.*?\})\s*```') {
        try {
            $jsonContent = $matches[1]
            $responseObj = $jsonContent | ConvertFrom-Json

            # Extract and format the main response
            if ($responseObj.response) {
                $formattedResponse = Format-JsonResponse -responseObj $responseObj
                return $formattedResponse
            }
        }
        catch {
            # If JSON parsing fails, fall back to general formatting
        }
    }

    # General formatting for non-JSON responses
    return Format-GeneralResponse -content $content
}

# Format JSON-structured responses in an organized way
function Format-JsonResponse {
    param($responseObj)

    $output = @()

    # Main response content
    if ($responseObj.response) {
        $mainResponse = $responseObj.response

        # Simple cleanup - just remove backticks and fix line breaks
        $mainResponse = $mainResponse -replace '`', ''  # Remove backticks
        $mainResponse = $mainResponse -replace '(\r?\n){3,}', "`n`n"  # Reduce excessive line breaks
        $mainResponse = $mainResponse.Trim()

        $output += $mainResponse
    }

    # Add reasoning if available and verbose mode is on
    if ($responseObj.reasoning -and $script:Verbose) {
        $output += ""
        $output += "Reasoning:"
        $output += "  $($responseObj.reasoning)"
    }

    # Add follow-up suggestions if available
    if ($responseObj.follow_up_suggestions -and $responseObj.follow_up_suggestions.Count -gt 0) {
        $output += ""
        $output += "Follow-up suggestions:"
        foreach ($suggestion in $responseObj.follow_up_suggestions) {
            $output += "  - $suggestion"
        }
    }

    # Add additional questions if available
    if ($responseObj.additional_questions -and $responseObj.additional_questions.Count -gt 0) {
        $output += ""
        $output += "Additional questions you might ask:"
        foreach ($question in $responseObj.additional_questions) {
            $output += "  - $question"
        }
    }

    return ($output -join "`n").Trim()
}



# Format general (non-JSON) responses
function Format-GeneralResponse {
    param($content)

    # Improve line breaks and spacing for better readability
    $content = $content -replace '(\r?\n){3,}', "`n`n"  # Reduce excessive line breaks
    $content = $content -replace '^\s+', ''  # Remove leading whitespace
    $content = $content -replace '\s+$', ''  # Remove trailing whitespace

    # Add proper indentation for structured content
    $lines = $content -split "`n"
    $formattedLines = @()

    foreach ($line in $lines) {
        $trimmedLine = $line.Trim()
        if ($trimmedLine) {
            # Add consistent indentation for list items and structured data
            if ($trimmedLine -match '^[-•*]\s+' -or $trimmedLine -match '^\d+\.\s+') {
                $formattedLines += "    $trimmedLine"
            }
            elseif ($trimmedLine -match '^[A-Za-z\s]+:$') {
                # Section headers
                $formattedLines += "`n$trimmedLine"
            }
            else {
                $formattedLines += $trimmedLine
            }
        }
        else {
            $formattedLines += ""
        }
    }

    return ($formattedLines -join "`n").Trim()
}

# Update conversation phase tracking with enhanced stage information
function Update-ConversationPhase {
    param($message, $stage = "")

    $oldPhase = $script:ConversationState.CurrentPhase
    $newPhase = $oldPhase

    # Determine phase based on stage prefix (much more reliable than content matching)
    if ($stage) {
        switch -Regex ($stage) {
            "^REQUEST_"           { $newPhase = "Request Processing" }
            "^PLANNING_"          { $newPhase = "Planning" }
            "^LLM_PROCESSING"     {
                $newPhase = "AI Processing"
                # Start spinner when AI processing begins
                if (-not $script:ConversationState.IsAIProcessing) {
                    Start-AISpinner -Message "AI is thinking"
                }
            }
            "^LLM_REQUEST"        {
                $newPhase = "AI Processing"
                # Start spinner when LLM request begins
                if (-not $script:ConversationState.IsAIProcessing) {
                    Start-AISpinner -Message "AI is thinking"
                }
            }
            "^LLM_PARSING"        {
                $newPhase = "AI Processing"
                # Keep spinner running during parsing
                if (-not $script:ConversationState.IsAIProcessing) {
                    Start-AISpinner -Message "AI is processing"
                }
            }
            "^LLM_RESPONSE"       {
                $newPhase = "AI Processing"
                # Stop spinner when AI processing completes
                if ($script:ConversationState.IsAIProcessing) {
                    Stop-AISpinner
                }
            }
            "^EXECUTION_COMPLETED" {
                $newPhase = "Complete"
                # Make sure spinner is stopped on completion
                if ($script:ConversationState.IsAIProcessing) {
                    Stop-AISpinner
                }
            }
            "^EXECUTION_"         { $newPhase = "Execution" }
        }
    }
    # Fallback to message type for non-stage messages
    elseif ($message.type -eq "Assistant Response") {
        $newPhase = "Complete"
    }
    elseif ($message.type -eq "User Input") {
        $newPhase = "Input"
    }

    $script:ConversationState.CurrentPhase = $newPhase

    # Show phase transitions
    if ($oldPhase -ne $newPhase -and $newPhase -ne "Initializing") {
        Show-PhaseTransition -newPhase $newPhase
    }
}

# Show elegant phase transitions
function Show-PhaseTransition {
    param($newPhase)

    $boxWidth = 77
    $content = "Phase: $newPhase"
    $padding = " " * [math]::Max(0, $boxWidth - $content.Length - 2)

    Write-Host ""
    Write-Host "  $([char]0x250C)$([string][char]0x2500 * $boxWidth)$([char]0x2510)" -ForegroundColor $Colors.Planning
    Write-Host "  $([char]0x2502) $content$padding $([char]0x2502)" -ForegroundColor $Colors.Planning
    Write-Host "  $([char]0x2514)$([string][char]0x2500 * $boxWidth)$([char]0x2518)" -ForegroundColor $Colors.Planning
    Write-Host ""
}

# Process conversation messages with elegant formatting
function Process-ConversationMessage {
    param($message)

    $script:ConversationState.LastActivity = Get-Date
    $script:ConversationState.MessageCount++
    $script:ConversationState.AllMessages += $message

    # Determine if this is a verbose message
    $isVerbose = $message.type -in @("Status Update", "LLM Inference") -and
                 $message.content -match "Analyzing|Processing|Sending"

    Show-Message -message $message -isVerbose $isVerbose

    # Check for completion
    if ($message.type -eq "Assistant Response") {
        $script:ConversationState.IsComplete = $true
        # Make sure spinner is stopped on completion
        if ($script:ConversationState.IsAIProcessing) {
            Stop-AISpinner
            Write-Host ""  # New line after spinner
        }
        Show-CompletionMessage
    }
    elseif ($message.type -eq "Error Message") {
        $script:ConversationState.HasError = $true
        $script:ConversationState.ErrorMessage = $message.content
        $script:ConversationState.IsComplete = $true
        # Make sure spinner is stopped on error
        if ($script:ConversationState.IsAIProcessing) {
            Stop-AISpinner
            Write-Host ""  # New line after spinner
        }
    }
}

# Show elegant completion message
function Show-CompletionMessage {
    $boxWidth = 75
    $content = "CONVERSATION COMPLETED SUCCESSFULLY"
    $padding = " " * [math]::Max(0, $boxWidth - $content.Length - 2)

    Write-Host ""
    Write-Host "  $([char]0x2554)$([string][char]0x2550 * $boxWidth)$([char]0x2557)" -ForegroundColor $Colors.Success
    Write-Host "  $([char]0x2551) $content$padding $([char]0x2551)" -ForegroundColor $Colors.Success
    Write-Host "  $([char]0x255A)$([string][char]0x2550 * $boxWidth)$([char]0x255D)" -ForegroundColor $Colors.Success
    Write-Host ""
}

# Enhanced polling with elegant progress indication
function Poll-ConversationMessages {
    param($context)

    # Update spinner if AI is processing
    if ($script:ConversationState.IsAIProcessing) {
        Update-AISpinner
    }

    try {
        $messagesJson = Get-AimxConversationMessages -Context $context
        if ($messagesJson -and $messagesJson -ne "[]") {
            $messages = $messagesJson | ConvertFrom-Json
            if ($messages -and $messages.Count -gt 0) {
                # If we have new messages and spinner is running, pause it temporarily to show messages
                $wasSpinning = $script:SpinnerRunning
                if ($wasSpinning) {
                    # Clear the spinner line cleanly without changing AI processing state
                    Write-Host "`r" -NoNewline
                    Write-Host (" " * 60) -NoNewline  # Clear with consistent padding
                    Write-Host "`r" -NoNewline
                    Write-Host ""  # New line after clearing spinner
                    $script:SpinnerRunning = $false
                }

                foreach ($message in $messages) {
                    Process-ConversationMessage -message $message
                }

                # If we paused the spinner and AI is still processing, restart it
                if ($wasSpinning -and $script:ConversationState.IsAIProcessing) {
                    $script:SpinnerRunning = $true
                    $script:ConversationState.SpinnerPosition = 0
                    $script:SpinnerStartTime = Get-Date  # Reset spinner timer when restarting
                    Write-Host "  $($script:SpinnerChars[0]) AI is thinking..." -NoNewline -ForegroundColor $Colors.Progress
                }

                return $messages.Count
            }
        }
        return 0
    }
    catch {
        # Stop spinner for error message
        if ($script:ConversationState.IsAIProcessing) {
            Stop-AISpinner
            Write-Host ""
        }

        Write-Host "  " -NoNewline
        Write-Host (Get-Date).ToString("HH:mm:ss.fff") -ForegroundColor $Colors.Timestamp -NoNewline
        Write-Host " [ERROR   ] Unable to retrieve messages: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return -1
    }
}

# Elegant system status display
function Show-SystemStatus {
    param($context)

    $boxWidth = 77
    $content = "System Status Check"
    $padding = " " * [math]::Max(0, $boxWidth - $content.Length - 2)

    Write-Host "  $([char]0x250C)$([string][char]0x2500 * $boxWidth)$([char]0x2510)" -ForegroundColor $Colors.Info
    Write-Host "  $([char]0x2502) $content$padding $([char]0x2502)" -ForegroundColor $Colors.Info
    Write-Host "  $([char]0x2514)$([string][char]0x2500 * $boxWidth)$([char]0x2518)" -ForegroundColor $Colors.Info
    Write-Host ""

    # LLM Status
    try {
        $llmStatusJson = Get-AimxLlmStatus -Context $context
        if ($llmStatusJson) {
            $llmStatus = $llmStatusJson | ConvertFrom-Json

            Write-Host "    LLM Connectivity: " -NoNewline -ForegroundColor $Colors.Info
            Write-Host $(if ($llmStatus.llmConnectivity) { "Connected" } else { "Disconnected" }) -ForegroundColor $(if ($llmStatus.llmConnectivity) { $Colors.Success } else { $Colors.Error })

            Write-Host "    LLM Instance: " -NoNewline -ForegroundColor $Colors.Info
            Write-Host $(if ($llmStatus.instanceInitialized) { "Initialized" } else { "Not Initialized" }) -ForegroundColor $(if ($llmStatus.instanceInitialized) { $Colors.Success } else { $Colors.Error })
        }
    } catch {
        Write-Host "    LLM Status: Unavailable" -ForegroundColor $Colors.Error
    }

    # MCP Server Status
    try {
        $mcpInfoJson = Get-AimxMcpServerInfo -Context $context
        if ($mcpInfoJson) {
            $mcpInfo = $mcpInfoJson | ConvertFrom-Json
            Write-Host "    MCP Servers: " -NoNewline -ForegroundColor $Colors.Info
            Write-Host "$($mcpInfo.summary.totalServers) total ($($mcpInfo.summary.inProcessServers) in-process, $($mcpInfo.summary.outOfProcessServers) out-of-process)" -ForegroundColor $Colors.Success

            Write-Host "    Available Tools: " -NoNewline -ForegroundColor $Colors.Info
            Write-Host $mcpInfo.summary.totalTools -ForegroundColor $Colors.Success

            if ($mcpInfo.servers -and $mcpInfo.servers.Count -gt 0) {
                foreach ($server in $mcpInfo.servers) {
                    Write-Host "       - $($server.name) ($($server.type)): $($server.toolCount) tools" -ForegroundColor $Colors.Info
                }
            }
        }
    } catch {
        Write-Host "    MCP Status: Unavailable" -ForegroundColor $Colors.Error
    }

    Write-Host ""
}

# Beautiful summary display for single query
function Show-QuerySummary {
    $duration = ((Get-Date) - $script:ConversationState.StartTime).TotalSeconds

    Write-Host ""
    Write-Host "  Query completed in " -NoNewline -ForegroundColor $Colors.Info
    Write-Host ("{0:F1} seconds" -f $duration) -NoNewline -ForegroundColor $Colors.Success
    Write-Host " with " -NoNewline -ForegroundColor $Colors.Info
    Write-Host $script:ConversationState.MessageCount -NoNewline -ForegroundColor $Colors.Success
    Write-Host " messages" -ForegroundColor $Colors.Info

    if ($script:ConversationState.HasError) {
        Write-Host "  Error: " -NoNewline -ForegroundColor $Colors.Error
        Write-Host $script:ConversationState.ErrorMessage -ForegroundColor $Colors.Error
    }
}

# Beautiful session summary display
function Show-SessionSummary {
    $sessionDuration = ((Get-Date) - $script:SessionStats.SessionStartTime).TotalSeconds

    $boxWidth = 75
    $title = "Chat Session Summary"
    $padding = " " * [math]::Max(0, $boxWidth - $title.Length - 2)

    Write-Host ""
    Write-Host "  $([char]0x2554)$([string][char]0x2550 * $boxWidth)$([char]0x2557)" -ForegroundColor $Colors.Header
    Write-Host "  $([char]0x2551) $title$padding $([char]0x2551)" -ForegroundColor $Colors.Header
    Write-Host "  $([char]0x255A)$([string][char]0x2550 * $boxWidth)$([char]0x255D)" -ForegroundColor $Colors.Header
    Write-Host ""

    Write-Host "  Session Duration: " -NoNewline -ForegroundColor $Colors.Info
    Write-Host ("{0:F1} seconds" -f $sessionDuration) -ForegroundColor $Colors.Success

    Write-Host "  Total Queries: " -NoNewline -ForegroundColor $Colors.Info
    Write-Host $script:SessionStats.TotalQueries -ForegroundColor $Colors.Success

    Write-Host "  Total Messages: " -NoNewline -ForegroundColor $Colors.Info
    Write-Host $script:SessionStats.TotalMessages -ForegroundColor $Colors.Success

    if ($script:SessionStats.Queries.Count -gt 0) {
        Write-Host ""
        Write-Host "  Query History:" -ForegroundColor $Colors.Info
        for ($i = 0; $i -lt [math]::Min(5, $script:SessionStats.Queries.Count); $i++) {
            $query = $script:SessionStats.Queries[$i]
            $truncatedQuery = if ($query.Length -gt 50) { $query.Substring(0, 50) + "..." } else { $query }
            Write-Host "    $($i + 1). $truncatedQuery" -ForegroundColor $Colors.Timestamp
        }
        if ($script:SessionStats.Queries.Count -gt 5) {
            Write-Host "    ... and $($script:SessionStats.Queries.Count - 5) more" -ForegroundColor $Colors.Timestamp
        }
    }

    Write-Host ""
}

# Execute a single query
function Invoke-SingleQuery {
    param([string]$Query)

    if ([string]::IsNullOrWhiteSpace($Query)) {
        return $false
    }

    # Reset state for new query
    Reset-ConversationState

    # Update session stats
    $script:SessionStats.TotalQueries++
    $script:SessionStats.Queries += $Query

    try {
        # Start conversation
        Write-Host ""
        Write-Host "  Processing query..." -ForegroundColor $Colors.Progress

        Start-AimxConversation -Context $script:GlobalContext -Query $Query

        # Main polling loop
        $consecutiveEmptyPolls = 0
        $maxEmptyPolls = 3600

        while (-not $script:ConversationState.IsComplete) {
            $messageCount = Poll-ConversationMessages -context $script:GlobalContext

            if ($messageCount -gt 0) {
                $consecutiveEmptyPolls = 0
            } elseif ($messageCount -eq 0) {
                $consecutiveEmptyPolls++
            } else {
                Write-Host "  [ERROR   ] Message polling failed" -ForegroundColor $Colors.Error
                $script:ConversationState.HasError = $true
                $script:ConversationState.ErrorMessage = "Message polling failed"
                break
            }

            # Check for timeout
            $elapsed = (Get-Date) - $script:ConversationState.StartTime
            if ($elapsed.TotalSeconds -gt $TimeoutSeconds) {
                Write-Host "  [TIMEOUT ] Query timeout after $TimeoutSeconds seconds" -ForegroundColor $Colors.Warning
                $script:ConversationState.HasError = $true
                $script:ConversationState.ErrorMessage = "Query timed out"
                break
            }

            # Check for completion via status
            if ($consecutiveEmptyPolls -gt 10) {
                try {
                    $statusJson = Get-AimxConversationStatus -Context $script:GlobalContext
                    if ($statusJson) {
                        $status = $statusJson | ConvertFrom-Json
                        if (-not $status.isActive -and $script:ConversationState.MessageCount -gt 0) {
                            $script:ConversationState.IsComplete = $true
                        }
                    }
                } catch {
                    # Status check failed, continue polling
                }
            }

            Start-Sleep -Milliseconds $PollIntervalMs
        }

        # Final message poll
        Poll-ConversationMessages -context $script:GlobalContext | Out-Null

        # Update session stats
        $script:SessionStats.TotalMessages += $script:ConversationState.MessageCount

        # Show query summary
        Show-QuerySummary

        # Stop the conversation to clean up
        try {
            Stop-AimxConversation -Context $script:GlobalContext
        } catch {
            # Ignore cleanup errors in chat mode
        }

        return -not $script:ConversationState.HasError

    } catch {
        Write-Host "  [EXCEPTION] $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return $false
    }
}

# Main execution
try {
    # Determine mode
    $mode = if ($SingleQuery -or $InitialQuery) { "Single Query Mode" } else { "Interactive Chat" }
    Show-Header -Mode $mode

    # Connect to AIMX service
    Write-Host "  Connecting to AIMX service..." -ForegroundColor $Colors.Progress
    $script:GlobalContext = Connect-AimxServer
    if (-not $script:GlobalContext) {
        throw "Failed to connect to AIMX server"
    }

    Write-Host "  Connected successfully (Session: " -NoNewline -ForegroundColor $Colors.Success
    Write-Host $script:GlobalContext.ContextId -NoNewline -ForegroundColor $Colors.Header
    Write-Host ")" -ForegroundColor $Colors.Success
    Write-Host ""

    # Show system status
    Show-SystemStatus -context $script:GlobalContext

    $exitCode = 0

    if ($SingleQuery -or ($InitialQuery -and -not $PSBoundParameters.ContainsKey('SingleQuery'))) {
        # Single query mode (backward compatibility)
        $queryToExecute = if ($InitialQuery) { $InitialQuery } else { "Hello from Rupo Zhang" }
        $success = Invoke-SingleQuery -Query $queryToExecute
        $exitCode = if ($success) { 0 } else { 1 }
    } else {
        # Interactive chat mode
        Write-Host "  Welcome to AIMX Interactive Chat!" -ForegroundColor $Colors.Success
        Write-Host "  Type your questions and press Enter. Use 'help' for assistance." -ForegroundColor $Colors.Info

        $isFirstInput = $true

        while ($true) {
            # Get user input
            $userQuery = Get-UserInput -IsFirstInput $isFirstInput
            $isFirstInput = $false

            # Check for exit
            if ($null -eq $userQuery) {
                Write-Host ""
                Write-Host "  Goodbye! Thanks for using AIMX Chat." -ForegroundColor $Colors.Success
                break
            }

            # Execute the query
            $success = Invoke-SingleQuery -Query $userQuery

            if (-not $success) {
                Write-Host "  Query failed. You can try again or type 'exit' to quit." -ForegroundColor $Colors.Warning
            }
        }
    }

} catch {
    Write-Host ""
    Write-Host "  [EXCEPTION] $($_.Exception.Message)" -ForegroundColor $Colors.Error
    $exitCode = 1
} finally {
    # Cleanup
    # Make sure spinner is stopped
    if ($script:ConversationState.IsAIProcessing) {
        Stop-AISpinner
        Write-Host ""
    }

    if ($script:GlobalContext) {
        try {
            # Stop any active conversation
            try {
                Stop-AimxConversation -Context $script:GlobalContext
            } catch {
                # Ignore if no active conversation
            }

            Close-AimxServer -Context $script:GlobalContext
            Write-Host "  Session cleanup completed" -ForegroundColor $Colors.Info
        } catch {
            Write-Host "  Cleanup issue: $($_.Exception.Message)" -ForegroundColor $Colors.Warning
        }
    }

    # Show session summary for interactive mode
    if (-not $SingleQuery -and $script:SessionStats.TotalQueries -gt 0) {
        Show-SessionSummary
    }

    Write-Host ""
    Write-Host "Exit code: $exitCode" -ForegroundColor $(if ($exitCode -eq 0) { $Colors.Success } else { $Colors.Error })
}

exit $exitCode
