{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "You are a PowerShell expert. Extract the goal for this IT management request that maps to a SINGLE PowerShell cmdlet.\n\nUser Request: \"is user rizhang locked out\"\n\nCRITICAL: Extract the EXACT identifiers from the user request - do not use placeholder names.\n\nCOMMAND EXAMPLES:\n- \"get domain password policy\" → Goal: Retrieve domain password policy using Get-ADDefaultDomainPasswordPolicy\n- \"show user account info\" → Goal: Display user account information using Get-ADUser\n- \"list domain controllers\" → Goal: List domain controllers using Get-ADDomainController\n\nExtract the goal preserving the exact user identifiers:\n\nRespond in JSON format:\n{\n  \"primaryObjective\": \"Execute [specific cmdlet] to [specific purpose with exact user identifiers]\",\n  \"successCriteria\": \"Command executes successfully and returns expected data\",\n  \"suggestedCommand\": \"PowerShell cmdlet name\",\n  \"parameters\": [\"param1\", \"param2\"],\n  \"outputType\": \"information|configuration|status\"\n}"}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}