{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "You are a PowerShell expert. This request maps to a SINGLE PowerShell cmdlet. Extract the goal with focus on the direct command execution.\n\nUser Request: \"get user information for linda<PERSON><PERSON>\"\n\nDIRECT COMMAND EXAMPLES:\n- \"get default domain password policy\" → Goal: Retrieve domain password policy settings using Get-ADDefaultDomainPasswordPolicy\n- \"show user john doe\" → Goal: Display user account information using Get-ADUser\n- \"list all domain controllers\" → Goal: List domain controllers using Get-ADDomainController\n\nExtract the goal for this direct command operation:\n\nRespond in JSON format:\n{\n  \"primaryObjective\": \"Execute [specific cmdlet] to [specific purpose]\",\n  \"successCriteria\": \"Command executes successfully and returns expected data\",\n  \"suggestedCommand\": \"PowerShell cmdlet name\",\n  \"parameters\": [\"param1\", \"param2\"],\n  \"outputType\": \"information|configuration|status\"\n}"}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}