# AIMX Enterprise Suite Installation Completion Script
param()

# Set console properties
$Host.UI.RawUI.WindowTitle = "AIMX Enterprise Suite - Installation Complete"

# Clear screen and show header
Clear-Host
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "   AIMX Enterprise Suite Installation Complete" -ForegroundColor Green  
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

Write-Host "Installation has completed successfully!" -ForegroundColor Yellow
Write-Host ""

Write-Host "Files installed to:" -ForegroundColor Cyan
Write-Host "  - Application: C:\ProgramData\Microsoft\AIMX\NetRagService\" -ForegroundColor White
Write-Host "  - System Files: C:\Windows\System32\" -ForegroundColor White
Write-Host ""

Write-Host "Service Status:" -ForegroundColor Cyan
try {
    $service = Get-Service -Name "AIMXSrv" -ErrorAction SilentlyContinue
    if ($service) {
        Write-Host "  - AIMX Service: Installed ($($service.Status))" -ForegroundColor White
    } else {
        Write-Host "  - AIMX Service: Not found" -ForegroundColor Red
    }
} catch {
    Write-Host "  - AIMX Service: Status unknown" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Run the Service Configuration Wizard (recommended)" -ForegroundColor White
Write-Host "  2. Start the AIMX service manually if needed" -ForegroundColor White
Write-Host ""
Write-Host "The Service Configuration Wizard will help you complete the foundry local setup." -ForegroundColor Yellow
Write-Host ""

# Ask user about running wizard
do {
    $choice = Read-Host "Would you like to run the Service Configuration Wizard now? (Y/N)"
    $choice = $choice.Trim().ToUpper()
} while ($choice -ne "Y" -and $choice -ne "N")

if ($choice -eq "Y") {
    Write-Host ""
    Write-Host "Starting AIMX Service Configuration Wizard..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Check if module file exists first
        $moduleFile = "C:\Windows\System32\aimx.psd1"
        if (-not (Test-Path $moduleFile)) {
            throw "AIMX PowerShell module not found at: $moduleFile"
        }

        Write-Host "Loading AIMX module..." -ForegroundColor Gray
        Import-Module $moduleFile -Force -ErrorAction Stop

        Write-Host "Checking for Initialize-AimxServiceWizard command..." -ForegroundColor Gray
        $command = Get-Command "Initialize-AimxServiceWizard" -ErrorAction SilentlyContinue
        if (-not $command) {
            throw "Initialize-AimxServiceWizard command not found in AIMX module"
        }

        Write-Host "Running wizard..." -ForegroundColor Gray
        Initialize-AimxServiceWizard

        Write-Host ""
        Write-Host "Wizard completed successfully." -ForegroundColor Green
    } catch {
        Write-Host ""
        Write-Host "ERROR: Failed to run wizard" -ForegroundColor Red
        Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Troubleshooting:" -ForegroundColor Yellow
        Write-Host "1. Check if AIMX module was installed correctly" -ForegroundColor White
        Write-Host "2. Try running as Administrator" -ForegroundColor White
        Write-Host "3. Check PowerShell execution policy" -ForegroundColor White
        Write-Host ""
        Write-Host "Manual command to try:" -ForegroundColor Yellow
        Write-Host "  powershell.exe -ExecutionPolicy Bypass -Command `"Import-Module 'C:\Windows\System32\aimx.psd1'; Initialize-AimxServiceWizard`"" -ForegroundColor White
        Write-Host ""
        Write-Host "Press any key to continue..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
} else {
    Write-Host ""
    Write-Host "You can run the Service Configuration Wizard later to complete the foundry local setup:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Option 1 - PowerShell Module:" -ForegroundColor Cyan
    Write-Host "  Import-Module AIMX" -ForegroundColor White
    Write-Host "  Initialize-AimxServiceWizard" -ForegroundColor White
    Write-Host ""
    Write-Host "Option 2 - Direct Command:" -ForegroundColor Cyan
    Write-Host "  powershell.exe -ExecutionPolicy Bypass -Command `"Import-Module 'C:\Windows\System32\aimx.psd1'; Initialize-AimxServiceWizard`"" -ForegroundColor White
    Write-Host ""
    Write-Host "Or start the service manually:" -ForegroundColor Cyan
    Write-Host "  sc start AIMXSrv" -ForegroundColor White
}

Write-Host ""
Write-Host "Installation complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
