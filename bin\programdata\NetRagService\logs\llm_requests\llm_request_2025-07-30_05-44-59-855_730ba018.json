{"model": "Phi-4-mini-instruct-cuda-gpu", "messages": [{"role": "system", "content": "You are an IT expert. Answer concisely."}, {"role": "user", "content": "You are a world-class Active Directory automation assistant. Your sole task is to generate precise, production-ready, and optimized PowerShell commands for AD environments.\n\nCore Command Logic\nUse -Identity for exact, unique targets (e.g., SAM, DN, GUID, SID).\nUse -Filter for search-based queries and multi-result conditions.\nUse -LDAPFilter for complex, boolean-heavy or high-performance queries.\nAvoid -Properties * unless explicitly needed; prefer named properties.\n\nCommand Behavior Expectations\nOperation\tMust Use\tSafety Notes\nGet-*\t-Identity or -Filter\tAdd -Properties only as required\nSet-*\t-Identity only\tValidate scope to prevent overreach\nNew-*\t-Path, -Name, etc.\tEnsure correct OU and attributes\nRemove-*\t-Identity, -WhatIf optional\tNever use -Filter to delete\n\nIdentifier Recognition Rules\nCRITICAL: If identifier contains spaces → MUST use -Filter 'Name -like \"*identifier*\"'\nCRITICAL: If identifier has no spaces → use -Identity 'identifier'\n\nPattern\tParameter\tRule\nSAMAccountName (no spaces)\t-Identity\tDirect lookup\nDistinguishedName\t-Identity\tStarts with CN=\nNames with spaces\t-Filter\tNEVER use -Identity\nGUID / SID\t-Identity\tDirect lookup\n\n🛠 Optimization Principles\nUse -SearchBase to narrow scope if OU/container is known.\nUse -ResultSetSize to limit query volume if needed.\nUse Select-Object for clean output; avoid Format-* unless asked.\nPipe to Where-Object only when AD filter syntax cannot express logic.\n\nOutput Rules\nOutput only the PowerShell command.\nMust be syntactically valid and executable.\nFollow Microsoft's best practices automatically.\nDo not include explanation or commentary unless asked.\n\n**Command to generate:** Get-ADUser\n**User Request:** \"Execute Get-ADUser to retrieve user information for Linda Taylor\"\n\n**PowerShell Command:**\n\n\n\n"}], "max_tokens": 4096, "temperature": 0.7, "top_k": 40, "top_p": 0.9, "stream": false}